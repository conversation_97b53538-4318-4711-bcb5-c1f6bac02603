package pkcs11

import (
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"strconv"

	"github.com/pkg/errors"
)

// Config holds the configuration parameters for connecting to a PKCS#11 device.
type Config struct {
	// LibraryPath is the filesystem path to the PKCS#11 library (.so, .dll, or .dylib)
	LibraryPath string

	// Identify specific slot(by TokenLabel or TokenSerialNumber or SlotID or SlotIndex)
	// TokenLabel and TokenSerialNumber are used to identify the token to use
	TokenLabel string
	// TokenSerialNumber is the serial number of the token to use
	TokenSerialNumber string
	// SlotID is the slot id of the PKCS#11 device to use
	SlotID uint
	// SlotIndex is the index of the slot to use (alternative to SlotID)
	SlotIndex uint

	// UserPIN is the PIN used to authenticate as a normal user (not SO)
	UserPIN string
}

// NewConfigFromEnv creates a new Config by reading configuration from environment variables.
//
// Environment variables:
//   - PKCS11_LIBRARY_PATH: Path to PKCS#11 library (default: auto-detect bundled SoftHSM)
//   - PKCS11_SLOT_ID: Slot ID to use (default: 0x0)
//   - PKCS11_USER_PIN: User PIN for authentication (required)
//
// If PKCS11_LIBRARY_PATH is not set, it will try to use the bundled SoftHSM library
// for the current platform.
//
// Returns an error if PKCS11_USER_PIN is not set or PKCS11_SLOT_ID is invalid.
func NewConfigFromEnv() (*Config, error) {
	config := &Config{}

	libraryPath := os.Getenv("PKCS11_LIBRARY_PATH")
	if libraryPath == "" {
		// Try to use bundled SoftHSM library
		bundledPath, err := getBundledSoftHSMPath()
		if err != nil {
			// Fall back to system default
			libraryPath = "/usr/lib/pkcs11/libpkcs11.so"
		} else {
			libraryPath = bundledPath
		}
	}
	config.LibraryPath = libraryPath

	slotIDStr := os.Getenv("PKCS11_SLOT_ID")
	if slotIDStr == "" {
		slotIDStr = "0"
	}
	slotID, err := strconv.ParseUint(slotIDStr, 10, 32)
	if err != nil {
		return nil, errors.Wrap(err, "invalid PKCS11_SLOT_ID")
	}
	config.SlotID = uint(slotID)

	userPIN := os.Getenv("PKCS11_USER_PIN")
	if userPIN == "" {
		return nil, errors.New("PKCS11_USER_PIN environment variable is required")
	}
	config.UserPIN = userPIN

	return config, nil
}

// NewConfig creates a new Config with the specified parameters.
func NewConfig(libraryPath string, slotID uint, userPIN string) *Config {
	return &Config{
		LibraryPath: libraryPath,
		SlotID:      slotID,
		UserPIN:     userPIN,
	}
}

// Validate checks that the configuration is valid and the library path exists.
// Returns an error if the library path is empty, the file doesn't exist, or the user PIN is empty.
func (c *Config) Validate() error {
	if c.LibraryPath == "" {
		return errors.New("library path cannot be empty")
	}

	if _, err := os.Stat(c.LibraryPath); os.IsNotExist(err) {
		return errors.Errorf("PKCS#11 library not found at: %s", c.LibraryPath)
	}

	if c.UserPIN == "" {
		return errors.New("user PIN cannot be empty")
	}

	return nil
}

// String returns a string representation of the config with the PIN redacted for security.
func (c *Config) String() string {
	return fmt.Sprintf("PKCS11Config{LibraryPath: %s, SlotID: %d, UserPIN: [REDACTED]}",
		c.LibraryPath, c.SlotID)
}

// getBundledSoftHSMPath returns the path to the bundled SoftHSM library for the current platform.
func getBundledSoftHSMPath() (string, error) {
	// Get current file's directory to locate the lib directory
	_, currentFile, _, ok := runtime.Caller(0)
	if !ok {
		return "", errors.New("could not determine current file path")
	}

	// Navigate to pkg/pkcs11/lib from current file location
	pkcs11Dir := filepath.Dir(currentFile)
	libDir := filepath.Join(pkcs11Dir, "lib")

	// Determine platform
	platform := runtime.GOOS + "-" + runtime.GOARCH

	// Convert Go architecture names to our naming convention
	switch runtime.GOARCH {
	case "amd64":
		// Keep as is
	case "arm64":
		// Keep as is
	default:
		return "", errors.Errorf("unsupported architecture: %s", runtime.GOARCH)
	}

	// Construct library path
	libPath := filepath.Join(libDir, platform, "libsofthsm2.so")

	// Check if file exists
	if _, err := os.Stat(libPath); os.IsNotExist(err) {
		return "", errors.Errorf("bundled SoftHSM library not found at: %s", libPath)
	}

	return libPath, nil
}

// NewTestConfig creates a configuration suitable for testing.
// It attempts to use the bundled SoftHSM library if available, otherwise falls back to mock.
func NewTestConfig() *Config {
	// Try to get bundled SoftHSM path
	libraryPath, err := getBundledSoftHSMPath()
	if err != nil {
		// Use a placeholder path for mock testing
		libraryPath = "/tmp/mock-pkcs11.so"
	}

	return &Config{
		LibraryPath: libraryPath,
		SlotID:      0,
		UserPIN:     "1234",
	}
}
