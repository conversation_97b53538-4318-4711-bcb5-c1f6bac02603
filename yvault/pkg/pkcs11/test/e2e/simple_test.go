package e2e

import (
	"os"
	"testing"

	"github.com/qcu266/labs/yvault/pkg/pkcs11"
)

func TestSoftHSMInstallation(t *testing.T) {
	// Test if SoftHSM is available
	if !IsSoftHSMAvailable() {
		t.Fatal("SoftHSM is not available after installation")
	}

	// Test SoftHSM setup
	config, cleanup := SetupSoftHSM(t)
	defer cleanup()

	// Verify config
	if config == nil {
		t.Fatal("SoftHSM config is nil")
	}

	if config.LibraryPath == "" {
		t.Fatal("Library path is empty")
	}

	t.Logf("SoftHSM library path: %s", config.LibraryPath)
	t.Logf("Slot ID: %d", config.SlotID)
	t.Logf("User PIN: %s", config.UserPIN)
	t.Logf("SoftHSM config Environment Variable: %s", os.Getenv("SOFTHSM2_CONF"))

	// Try to create a client
	client, err := pkcs11.NewClient(config)
	if err != nil {
		t.Fatalf("Failed to create PKCS#11 client: %v", err)
	}
	defer client.Close()

	t.Log("SoftHSM installation and setup successful!")
}
