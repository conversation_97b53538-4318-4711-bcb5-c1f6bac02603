package pkcs11

import (
	"os"
	"testing"
)

func TestNewClient(t *testing.T) {

	config := NewConfig("/Users/<USER>/spaces/labs/yvault/pkg/pkcs11/test/e2e/softhsmv2/build/lib/softhsm/libsofthsm2.so", 0, "1234")
	os.Setenv("SOFTHSM2_CONF", "/Users/<USER>/spaces/labs/yvault/pkg/pkcs11/test/e2e/test-temp/tokens-1751096922985444000/softhsm.conf")

	client, err := NewClient(config)
	if err != nil {
		t.Fatalf("Failed to create PKCS#11 client: %v", err)
	}
	defer client.Close()
	t.Logf("PKCS#11 client created successfully")
}
